{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ system_settings.site_name }} - {{ system_settings.site_title }}{% endblock %}</title>
    
    <!-- Bootstrap 5.3.3 CSS (本地) -->
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
    <!-- Bootstrap Icons (本地) -->
    <link href="{% static 'css/bootstrap-icons.min.css' %}" rel="stylesheet">

    <!-- Three Dots Loading Animation (本地) -->
    <link href="{% static 'css/typing-indicator.css' %}" rel="stylesheet">

    <!-- Animate.css (本地) -->
    <link href="{% static 'css/animate.min.css' %}" rel="stylesheet">

    <!-- Ant Design X 主题样式 (本地) -->
    <link href="{% static 'css/antdx-theme.css' %}" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        :root {
            /* Ant Design X 主题配色 */
            --antdx-primary: #1677ff;
            --antdx-primary-hover: #4096ff;
            --antdx-primary-active: #0958d9;
            --antdx-primary-bg: rgba(22, 119, 255, 0.06);

            /* 渐变色（保留一些渐变效果用于装饰） */
            --antdx-gradient-primary: linear-gradient(135deg, #1677ff 0%, #4096ff 100%);
            --antdx-gradient-secondary: linear-gradient(135deg, #722ed1 0%, #eb2f96 100%);

            /* 背景色 */
            --antdx-bg-container: #ffffff;
            --antdx-bg-layout: #f5f5f5;
            --antdx-bg-elevated: #ffffff;

            /* 文字颜色 */
            --antdx-text-primary: #262626;
            --antdx-text-secondary: #8c8c8c;
            --antdx-text-tertiary: #bfbfbf;

            /* 边框和分割线 */
            --antdx-border: #d9d9d9;
            --antdx-border-light: #f0f0f0;

            /* 阴影 */
            --antdx-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
            --antdx-shadow-md: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
            --antdx-shadow-lg: 0 16px 48px 16px rgba(0, 0, 0, 0.08), 0 12px 32px rgba(0, 0, 0, 0.12), 0 8px 16px -8px rgba(0, 0, 0, 0.16);

            /* 圆角 */
            --antdx-radius-sm: 4px;
            --antdx-radius-md: 6px;
            --antdx-radius-lg: 8px;
            --antdx-radius-xl: 16px;

            /* 兼容旧变量 */
            --primary-gradient: var(--antdx-gradient-primary);
            --secondary-gradient: var(--antdx-gradient-secondary);
            --glass-bg: var(--antdx-bg-elevated);
            --glass-border: var(--antdx-border-light);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: var(--antdx-bg-layout);
            color: var(--antdx-text-primary);
            min-height: 100vh;
        }

        /* Ant Design X 风格的卡片 */
        .glass-card {
            background: var(--antdx-bg-elevated);
            backdrop-filter: blur(10px);
            border: 1px solid var(--antdx-border-light);
            border-radius: var(--antdx-radius-xl);
            box-shadow: var(--antdx-shadow-md);
        }

        /* Ant Design X 风格的按钮 */
        .btn-gradient {
            background: var(--antdx-gradient-primary);
            border: 1px solid var(--antdx-primary);
            color: white;
            font-weight: 500;
            border-radius: var(--antdx-radius-md);
            transition: all 0.2s ease;
        }

        .btn-gradient:hover {
            background: var(--antdx-primary-hover);
            border-color: var(--antdx-primary-hover);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--antdx-shadow-sm);
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
            color: white;
        }

        /* Ant Design X 风格的导航栏 */
        .navbar-glass {
            background: var(--antdx-bg-elevated);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--antdx-border-light);
            box-shadow: var(--antdx-shadow-sm);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 20px;
            color: var(--antdx-text-primary);
        }

        .navbar-brand:hover {
            color: var(--antdx-primary);
        }

        /* 用户头像下拉菜单样式 */
        .dropdown-toggle::after {
            display: none !important;
        }

        .dropdown-menu.glass-card {
            margin-top: 8px;
        }

        .dropdown-item {
            padding: 8px 16px;
            transition: all 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: rgba(0, 123, 255, 0.1);
            transform: translateX(4px);
        }

        .dropdown-item.text-danger:hover {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545 !important;
        }

        /* 使用Animate.css，不需要自定义keyframes */

        /* 下拉菜单定位 */
        .dropdown-menu {
            z-index: 1000;
            border: 1px solid var(--antdx-border-light);
            border-radius: var(--antdx-radius-lg);
            box-shadow: var(--antdx-shadow-md);
        }

        /* Ant Design X 风格的表单控件 */
        .form-control {
            border-radius: var(--antdx-radius-md);
            border: 1px solid var(--antdx-border);
            padding: 8px 12px;
            font-size: 14px;
            transition: all 0.2s ease;
            color: var(--antdx-text-primary);
        }

        .form-control:focus {
            border-color: var(--antdx-primary);
            box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
            outline: none;
        }

        /* Ant Design X 风格的模态框 */
        .modal-content {
            border: none;
            border-radius: var(--antdx-radius-xl);
            box-shadow: var(--antdx-shadow-lg);
        }

        /* Ant Design X 风格的警告框 */
        .alert {
            border-radius: var(--antdx-radius-lg);
            border: 1px solid var(--antdx-border-light);
        }

        /* 加载遮罩 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        {% block extra_css %}{% endblock %}
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-glass {% if not hide_navbar %}fixed-top{% endif %}">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <i class="bi bi-robot fs-3 text-primary me-2"></i>
                <span class="fw-bold">{{ system_settings.site_name }}</span>
            </a>
            
            <div class="d-flex align-items-center">
                {% if user.is_authenticated %}
                    <span class="me-3">欢迎，{{ user.username }}</span>


                    <!-- 用户头像下拉菜单 -->
                    <div class="dropdown">
                        <button class="bg-primary rounded-circle d-flex align-items-center justify-content-center dropdown-toggle border-0"
                                style="width: 40px; height: 40px; color: white; cursor: pointer;"
                                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            {{ user.username|first|upper }}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end glass-card border-0 shadow-lg" style="min-width: 160px;">
                            <li>
                                <h6 class="dropdown-header fw-bold">{{ user.username }}</h6>
                            </li>
                            {% if user.is_staff %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="{% url 'agents' %}">
                                    <i class="bi bi-robot me-2"></i>智能体管理
                                </a>
                            </li>
                            {% if system_settings.enable_search %}
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="{% url 'knowledge-bases' %}">
                                    <i class="bi bi-book me-2"></i>知识库管理
                                </a>
                            </li>
                            {% endif %}
                            {% if user.is_superuser %}
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="{% url 'llm-settings' %}">
                                    <i class="bi bi-cpu me-2"></i>大模型设置
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="{% url 'admin-settings' %}">
                                    <i class="bi bi-gear-fill me-2"></i>管理员设置
                                </a>
                            </li>
                            {% endif %}
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center text-danger" href="#" onclick="handleLogout()">
                                    <i class="bi bi-box-arrow-right me-2"></i>注销
                                </a>
                            </li>
                        </ul>
                    </div>
                {% else %}
                    <button class="btn btn-outline-primary me-3" onclick="showLoginModal()">
                        登录
                    </button>
                    {% if system_settings.allow_registration %}
                    <button class="btn btn-gradient" onclick="showRegisterModal()">
                        注册
                    </button>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main {% if not hide_navbar %}style="padding-top: 76px;"{% endif %}>
        {% block content %}{% endblock %}
    </main>

    <!-- 登录模态框 -->
    {% if not user.is_authenticated %}
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content glass-card">
                <div class="modal-header border-0">
                    <h5 class="modal-title fw-bold">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm" onsubmit="handleLogin(event)">
                        <div class="mb-3">
                            <label class="form-label fw-bold">用户名</label>
                            <input type="text" class="form-control" id="loginUsername" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" form="loginForm" class="btn btn-gradient">登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    {% if system_settings.allow_registration %}
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content glass-card">
                <div class="modal-header border-0">
                    <h5 class="modal-title fw-bold">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">用户名</label>
                                <input type="text" class="form-control" id="registerUsername" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">邮箱</label>
                                <input type="email" class="form-control" id="registerEmail" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">密码</label>
                                <input type="password" class="form-control" id="registerPassword" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">确认密码</label>
                                <input type="password" class="form-control" id="registerPasswordConfirm" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-gradient" onclick="handleRegister()">注册</button>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% endif %}

    <!-- Bootstrap 5.3.3 JS (本地) -->
    <script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>

    <!-- Autosize JS (本地) -->
    <script src="{% static 'js/autosize.min.js' %}"></script>

    <!-- List.js for search and filtering (本地) -->
    <script src="{% static 'js/list.min.js' %}"></script>

    <!-- Marked.js for Markdown parsing (本地) -->
    <script src="{% static 'js/marked.min.js' %}"></script>
    
    <!-- 全局JavaScript -->
    <script>
        // API基础URL
        const API_BASE_URL = '/api';
        
        // 使用Django内置的CSRF处理
        const csrftoken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                         document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.cookie.match(/csrftoken=([^;]+)/)?.[1];
        
        // 显示登录模态框
        function showLoginModal() {
            const modal = new bootstrap.Modal(document.getElementById('loginModal'));
            modal.show();
        }
        
        // 显示注册模态框
        function showRegisterModal() {
            const registerModal = document.getElementById('registerModal');
            if (!registerModal) {
                alert('用户注册功能已被管理员禁用');
                return;
            }
            const modal = new bootstrap.Modal(registerModal);
            modal.show();
        }
        
        // 处理登录
        function handleLogin(event) {
            // 如果是表单提交事件，阻止默认行为
            if (event) {
                event.preventDefault();
            }

            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value;

            // 基本验证
            if (!username || !password) {
                alert('请输入用户名和密码');
                return;
            }

            // 禁用登录按钮防止重复提交
            const loginBtn = document.querySelector('#loginModal .btn-gradient');
            const originalText = loginBtn.textContent;
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';

            fetch(`${API_BASE_URL}/auth/login/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrftoken
                },
                body: JSON.stringify({ username, password })
            })
            .then(response => response.json())
            .then(data => {
                if (data.token) {
                    localStorage.setItem('token', data.token);
                    location.reload();
                } else {
                    alert('登录失败：' + (data.message || '用户名或密码错误'));
                }
            })
            .catch(error => {
                console.error('登录错误:', error);
                alert('登录失败，请稍后重试');
            })
            .finally(() => {
                // 恢复登录按钮状态
                loginBtn.disabled = false;
                loginBtn.textContent = originalText;
            });
        }
        
        // 处理注册
        function handleRegister() {
            const username = document.getElementById('registerUsername').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const passwordConfirm = document.getElementById('registerPasswordConfirm').value;

            if (password !== passwordConfirm) {
                alert('密码不匹配');
                return;
            }

            fetch(`${API_BASE_URL}/auth/register/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrftoken
                },
                body: JSON.stringify({
                    username,
                    email,
                    password,
                    password_confirm: passwordConfirm
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.token) {
                    localStorage.setItem('token', data.token);
                    location.reload();
                } else {
                    alert('注册失败：' + JSON.stringify(data));
                }
            })
            .catch(error => {
                console.error('注册错误:', error);
                alert('注册失败，请稍后重试');
            });
        }

        // 处理注销
        function handleLogout() {
            if (confirm('确定要注销吗？')) {
                fetch(`${API_BASE_URL}/auth/logout/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken,
                        'Authorization': 'Token ' + localStorage.getItem('token')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // 清除本地存储的token
                    localStorage.removeItem('token');
                    // 跳转到首页
                    window.location.href = '/';
                })
                .catch(error => {
                    console.error('注销错误:', error);
                    // 即使出错也清除token并跳转到首页
                    localStorage.removeItem('token');
                    window.location.href = '/';
                });
            }
        }
        
        // 显示设置模态框（占位函数）
        function showSettingsModal() {
            alert('设置功能正在开发中...');
        }

        // Bootstrap下拉菜单会自动初始化，无需手动处理
        document.addEventListener('DOMContentLoaded', function() {
            // 检查Bootstrap是否正确加载
            if (typeof bootstrap === 'undefined') {
                console.error('❌ Bootstrap未正确加载！');
                return;
            }

            console.log('✅ Bootstrap已加载，下拉菜单将自动工作');
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
